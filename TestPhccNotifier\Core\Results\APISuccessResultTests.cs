using Core.Results;
using System.Text.Json;

namespace TestPhccNotifier.Core.Results;

public class APISuccessResultTests
{
    [Fact]
    public void DefaultConstructor_ShouldCreateEmptyResult()
    {
        // Act
        var result = new APISuccessResult<string>();

        // Assert
        result.Data.Should().BeNull();
        result.Message.Should().BeNull();
    }

    [Fact]
    public void Constructor_WithData_ShouldSetData()
    {
        // Arrange
        const string testData = "Test data";

        // Act
        var result = new APISuccessResult<string>(testData);

        // Assert
        result.Data.Should().Be(testData);
        result.Message.Should().BeNull();
    }

    [Fact]
    public void Constructor_WithMessage_ShouldSetMessage()
    {
        // Arrange
        const string testMessage = "Operation successful";

        // Act
        var result = new APISuccessResult<int>(testMessage);

        // Assert
        result.Message.Should().Be(testMessage);
        result.Data.Should().Be(0); // default value for int
    }

    [Fact]
    public void Constructor_WithDataAndMessage_ShouldSetBoth()
    {
        // Arrange
        const string testData = "Test data";
        const string testMessage = "Operation successful";

        // Act
        var result = new APISuccessResult<string>(testData, testMessage);

        // Assert
        result.Data.Should().Be(testData);
        result.Message.Should().Be(testMessage);
    }

    [Fact]
    public void Constructor_WithComplexObject_ShouldSetData()
    {
        // Arrange
        var testObject = new { Id = 1, Name = "Test", IsActive = true };

        // Act
        var result = new APISuccessResult<object>(testObject);

        // Assert
        result.Data.Should().Be(testObject);
        result.Message.Should().BeNull();
    }

    [Fact]
    public void Constructor_WithNullData_ShouldSetNullData()
    {
        // Act
        var result = new APISuccessResult<string>((string)null!);

        // Assert
        result.Data.Should().BeNull();
        result.Message.Should().BeNull();
    }

    [Fact]
    public void Serialization_WithNullValues_ShouldIgnoreNullProperties()
    {
        // Arrange
        var result = new APISuccessResult<string>();
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Act
        var json = JsonSerializer.Serialize(result, options);

        // Assert
        json.Should().Be("{}");
    }

    [Fact]
    public void Serialization_WithData_ShouldIncludeData()
    {
        // Arrange
        var result = new APISuccessResult<string>("test data");
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Act
        var json = JsonSerializer.Serialize(result, options);

        // Assert
        json.Should().Contain("\"data\":\"test data\"");
    }

    [Fact]
    public void Serialization_WithMessage_ShouldIncludeMessage()
    {
        // Arrange
        var result = new APISuccessResult<int>("Success message");
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Act
        var json = JsonSerializer.Serialize(result, options);

        // Assert
        json.Should().Contain("\"message\":\"Success message\"");
    }

    [Fact]
    public void APISuccessResult_ShouldImplementIAPISuccessResult()
    {
        // Arrange
        var result = new APISuccessResult<string>();

        // Assert
        result.Should().BeAssignableTo<IAPISuccessResult>();
    }
}
