using Core.Exceptions;

namespace TestPhccNotifier.Core.Exceptions;

public class AuthenticationExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_ShouldSetMessage()
    {
        // Arrange
        const string expectedMessage = "Authentication failed";

        // Act
        var exception = new AuthenticationException(expectedMessage);

        // Assert
        exception.Message.Should().Be(expectedMessage);
        exception.Should().BeOfType<AuthenticationException>();
        exception.Should().BeAssignableTo<Exception>();
    }

    [Theory]
    [InlineData("X-Client-Id parameter is missing.")]
    [InlineData("X-Secret-Key parameter is missing.")]
    [InlineData("ClientId or SecretKey is invalid.")]
    [InlineData("This account has been disabled.")]
    public void Constructor_WithAuthenticationMessages_ShouldSetMessageCorrectly(string message)
    {
        // Act
        var exception = new AuthenticationException(message);

        // Assert
        exception.Message.Should().Be(message);
    }

    [Fact]
    public void Constructor_WithEmptyMessage_ShouldSetEmptyMessage()
    {
        // Act
        var exception = new AuthenticationException("");

        // Assert
        exception.Message.Should().Be("");
    }

    [Fact]
    public void Constructor_WithNullMessage_ShouldHandleGracefully()
    {
        // Act & Assert
        var exception = new AuthenticationException(null!);
        exception.Should().NotBeNull();
        exception.Message.Should().BeNull();
    }

    [Fact]
    public void AuthenticationException_ShouldInheritFromException()
    {
        // Arrange
        var exception = new AuthenticationException("Test");

        // Assert
        exception.Should().BeAssignableTo<Exception>();
    }

    [Fact]
    public void AuthenticationException_ShouldBeSerializable()
    {
        // Arrange
        const string message = "Authentication error";
        var originalException = new AuthenticationException(message);

        // Act & Assert
        originalException.Should().BeBinarySerializable();
    }
}
