﻿using MediatR;

namespace Domain.Features.Message.Commands.RescheduleAppointment
{
    public class RescheduleAppointmentRequest : IRequest<RescheduleAppointmentResponse>
    {
        public string AppointmentIdentifier { get; set; }
        public string AppointmentType { get; set; }
        public string? ProcedureName { get; set; }
        public string? ProcedureCode { get; set; }
        public string AppointmentLocation { get; set; }
        public string AppointmentLocationDescription { get; set; }
        public DateTime AppointmentDateTime { get; set; }
        public int AppointmentArriveMinutesBefore { get; set; }
        public string CustomIdentifier { get; set; }
        public string SubjectIdentifier { get; set; }
        public string RecipientFirstName { get; set; }
        public string RecipientLastName { get; set; }
        public string RecipientPhone { get; set; }
        public string RecipientEmail { get; set; }
        public DateTime SendDateTime { get; set; }
        public int ReminderSendHoursBefore { get; set; }
        public string ReminderType { get; set; }
        public string RecipientNationality { get; set; }
    }
}
