using Domain.Features.Message.Commands.NewAppointment;
using FluentValidation.TestHelper;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Domain.Features.Message.Commands.NewAppointment;

public class NewAppointmentValidatorTests
{
    private readonly NewAppointmentRequestValidator _validator;

    public NewAppointmentValidatorTests()
    {
        _validator = new NewAppointmentRequestValidator();
    }

    [Fact]
    public void Validate_WithValidRequest_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var request = CreateValidRequest();

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyAppointmentIdentifier_ShouldHaveValidationError(string appointmentIdentifier)
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentIdentifier = appointmentIdentifier;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentIdentifier)
            .WithErrorMessage("AppointmentIdentifier can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyAppointmentType_ShouldHaveValidationError(string appointmentType)
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentType = appointmentType;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentType)
            .WithErrorMessage("AppointmentType can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyAppointmentLocation_ShouldHaveValidationError(string appointmentLocation)
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentLocation = appointmentLocation;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentLocation)
            .WithErrorMessage("AppointmentLocation can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyAppointmentLocationDescription_ShouldHaveValidationError(string description)
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentLocationDescription = description;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentLocationDescription)
            .WithErrorMessage("AppointmentLocationDescription can not be empty.");
    }

    [Fact]
    public void Validate_WithPastAppointmentDateTime_ShouldHaveValidationError()
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentDateTime = DateTime.Now.AddDays(-1);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentDateTime)
            .WithErrorMessage("AppointmentDateTime must be greater than now.");
    }

    [Fact]
    public void Validate_WithEmptyAppointmentDateTime_ShouldHaveValidationError()
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentDateTime = default;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentDateTime)
            .WithErrorMessage("AppointmentDateTime can not be empty.");
    }

    [Fact]
    public void Validate_WithEmptyAppointmentArriveMinutesBefore_ShouldHaveValidationError()
    {
        // Arrange
        var request = CreateValidRequest();
        request.AppointmentArriveMinutesBefore = 0;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AppointmentArriveMinutesBefore)
            .WithErrorMessage("AppointmentArriveMinutesBefore can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("HC123")] // Too short
    [InlineData("HC12345678901")] // Too long
    [InlineData("AB1234567890")] // Doesn't start with HC
    public void Validate_WithInvalidCustomIdentifier_ShouldHaveValidationError(string customIdentifier)
    {
        // Arrange
        var request = CreateValidRequest();
        request.CustomIdentifier = customIdentifier;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        if (string.IsNullOrEmpty(customIdentifier))
        {
            result.ShouldHaveValidationErrorFor(x => x.CustomIdentifier)
                .WithErrorMessage("CustomIdentifier can not be empty.");
        }
        else if (customIdentifier.Length != 10)
        {
            result.ShouldHaveValidationErrorFor(x => x.CustomIdentifier)
                .WithErrorMessage("The CustomIdentitifer length must be 10 characters.");
        }
        else if (!customIdentifier.StartsWith("HC"))
        {
            result.ShouldHaveValidationErrorFor(x => x.CustomIdentifier)
                .WithErrorMessage("CustomIdentifier must starts with HC");
        }
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptySubjectIdentifier_ShouldHaveValidationError(string subjectIdentifier)
    {
        // Arrange
        var request = CreateValidRequest();
        request.SubjectIdentifier = subjectIdentifier;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SubjectIdentifier)
            .WithErrorMessage("SubjectIdentifier can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyRecipientFirstName_ShouldHaveValidationError(string firstName)
    {
        // Arrange
        var request = CreateValidRequest();
        request.RecipientFirstName = firstName;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.RecipientFirstName)
            .WithErrorMessage("RecipientFirstName can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyRecipientLastName_ShouldHaveValidationError(string lastName)
    {
        // Arrange
        var request = CreateValidRequest();
        request.RecipientLastName = lastName;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.RecipientLastName)
            .WithErrorMessage("RecipientLastName can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyRecipientMobileNumber_ShouldHaveValidationError(string mobileNumber)
    {
        // Arrange
        var request = CreateValidRequest();
        request.RecipientMobileNumber = mobileNumber;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.RecipientMobileNumber)
            .WithErrorMessage("RecipientMobileNumber can not be empty.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Validate_WithEmptyRecipientNationality_ShouldHaveValidationError(string nationality)
    {
        // Arrange
        var request = CreateValidRequest();
        request.RecipientNationality = nationality;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.RecipientNationality)
            .WithErrorMessage("RecipientNationality can not be empty.");
    }

    [Fact]
    public void Validate_WithEmptyReminderSendHoursBefore_ShouldHaveValidationError()
    {
        // Arrange
        var request = CreateValidRequest();
        request.ReminderSendHoursBefore = 0;

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ReminderSendHoursBefore)
            .WithErrorMessage("ReminderSendHoursBefore can not be empty.");
    }

    [Fact]
    public void Validate_WithTooLongProcedureName_ShouldHaveValidationError()
    {
        // Arrange
        var request = CreateValidRequest();
        request.ProcedureName = new string('A', 201); // 201 characters

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ProcedureName)
            .WithErrorMessage("ProcedureName must not exceed 200 characters.");
    }

    [Fact]
    public void Validate_WithTooLongProcedureCode_ShouldHaveValidationError()
    {
        // Arrange
        var request = CreateValidRequest();
        request.ProcedureCode = new string('A', 51); // 51 characters

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ProcedureCode)
            .WithErrorMessage("ProcedureCode must not exceed 50 characters.");
    }

    private NewAppointmentRequest CreateValidRequest()
    {
        return new NewAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari",
            ReminderSendHoursBefore = 24,
            ProcedureName = "Mammography",
            ProcedureCode = "MAM001"
        };
    }
}
