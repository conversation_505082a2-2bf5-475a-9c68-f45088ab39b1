using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

namespace TestPhccNotifier.TestUtilities;

public interface IServiceClientWrapper
{
    bool IsReady { get; }
    Task<EntityCollection> RetrieveMultipleAsync(QueryExpression query);
    Entity? RetrieveFirst(QueryExpression query);
    Task<Guid> CreateAsync(Entity entity, CancellationToken cancellationToken);
    Task<Entity> RetrieveAsync(string entityName, Guid id, ColumnSet columnSet, CancellationToken cancellationToken);
}
