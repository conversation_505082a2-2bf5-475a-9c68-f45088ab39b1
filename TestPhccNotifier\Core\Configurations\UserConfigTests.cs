using Core.Configurations;

namespace TestPhccNotifier.Core.Configurations;

public class UserConfigTests
{
    [Fact]
    public void UserConfig_ShouldHaveDefaultValues()
    {
        // Act
        var userConfig = new UserConfig();

        // Assert
        userConfig.Id.Should().Be(Guid.Empty);
        userConfig.Name.Should().BeNull();
        userConfig.ClientId.Should().BeNull();
        userConfig.SecretKey.Should().BeNull();
        userConfig.IsActive.Should().BeFalse();
    }

    [Fact]
    public void UserConfig_ShouldAllowSettingAllProperties()
    {
        // Arrange
        var id = Guid.NewGuid();
        const string name = "Test User";
        const string clientId = "test-client";
        const string secretKey = "test-secret";
        const bool isActive = true;

        // Act
        var userConfig = new UserConfig
        {
            Id = id,
            Name = name,
            ClientId = clientId,
            SecretKey = secretKey,
            IsActive = isActive
        };

        // Assert
        userConfig.Id.Should().Be(id);
        userConfig.Name.Should().Be(name);
        userConfig.ClientId.Should().Be(clientId);
        userConfig.SecretKey.Should().Be(secretKey);
        userConfig.IsActive.Should().Be(isActive);
    }

    [Fact]
    public void UserConfig_ShouldAllowNullStringProperties()
    {
        // Act
        var userConfig = new UserConfig
        {
            Name = null,
            ClientId = null,
            SecretKey = null
        };

        // Assert
        userConfig.Name.Should().BeNull();
        userConfig.ClientId.Should().BeNull();
        userConfig.SecretKey.Should().BeNull();
    }

    [Fact]
    public void UserConfig_ShouldAllowEmptyStringProperties()
    {
        // Act
        var userConfig = new UserConfig
        {
            Name = "",
            ClientId = "",
            SecretKey = ""
        };

        // Assert
        userConfig.Name.Should().Be("");
        userConfig.ClientId.Should().Be("");
        userConfig.SecretKey.Should().Be("");
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void UserConfig_IsActive_ShouldAcceptBooleanValues(bool isActive)
    {
        // Act
        var userConfig = new UserConfig { IsActive = isActive };

        // Assert
        userConfig.IsActive.Should().Be(isActive);
    }

    [Fact]
    public void UserConfig_Id_ShouldAcceptValidGuid()
    {
        // Arrange
        var validGuid = Guid.Parse("863183B2-A908-4B60-B705-EEB3078288B2");

        // Act
        var userConfig = new UserConfig { Id = validGuid };

        // Assert
        userConfig.Id.Should().Be(validGuid);
    }

    [Fact]
    public void UserConfig_Properties_ShouldBeReadWritable()
    {
        // Arrange
        var userConfig = new UserConfig();
        var newId = Guid.NewGuid();

        // Act & Assert - Test that properties can be read and written
        userConfig.Id = newId;
        userConfig.Id.Should().Be(newId);

        userConfig.Name = "Updated Name";
        userConfig.Name.Should().Be("Updated Name");

        userConfig.ClientId = "updated-client";
        userConfig.ClientId.Should().Be("updated-client");

        userConfig.SecretKey = "updated-secret";
        userConfig.SecretKey.Should().Be("updated-secret");

        userConfig.IsActive = true;
        userConfig.IsActive.Should().BeTrue();
    }
}
