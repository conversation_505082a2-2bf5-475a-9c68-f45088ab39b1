using Domain.Features.Message;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Domain.Features.Message;

public class MessageManagerTests
{
    private readonly Mock<ServiceClient> _mockServiceClient;
    private readonly MessageManager _messageManager;

    public MessageManagerTests()
    {
        _mockServiceClient = TestHelper.CreateMockServiceClient();
        _messageManager = new MessageManager(_mockServiceClient.Object);
    }

    [Theory]
    [InlineData("Bahraini", "Arabic")]
    [InlineData("Egyptian", "Arabic")]
    [InlineData("Iraqi", "Arabic")]
    [InlineData("Jordanian", "Arabic")]
    [InlineData("Kuwaiti", "Arabic")]
    [InlineData("Lebanese", "Arabic")]
    [InlineData("Mauritania", "Arabic")]
    [InlineData("Moroccan", "Arabic")]
    [InlineData("Omani", "Arabic")]
    [InlineData("Palestinian", "Arabic")]
    [InlineData("Qatar Document", "Arabic")]
    [InlineData("Qatari", "Arabic")]
    [InlineData("Romanian", "Arabic")]
    [InlineData("Saudi", "Arabic")]
    [InlineData("Sudanese", "Arabic")]
    [InlineData("Syrian", "Arabic")]
    [InlineData("Tunisian", "Arabic")]
    [InlineData("Yemeni", "Arabic")]
    public void NationalityCheck_WithArabicNationalities_ShouldReturnArabic(string nationality, string expected)
    {
        // Act
        var result = _messageManager.NationalityCheck(nationality);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("American", "English")]
    [InlineData("British", "English")]
    [InlineData("Canadian", "English")]
    [InlineData("Indian", "English")]
    [InlineData("Filipino", "English")]
    [InlineData("German", "English")]
    [InlineData("French", "English")]
    [InlineData("Unknown", "English")]
    [InlineData("", "English")]
    [InlineData(null, "English")]
    public void NationalityCheck_WithNonArabicNationalities_ShouldReturnEnglish(string nationality, string expected)
    {
        // Act
        var result = _messageManager.NationalityCheck(nationality);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void NationalityCheck_WithCaseSensitivity_ShouldBeExact()
    {
        // Arrange & Act & Assert
        _messageManager.NationalityCheck("qatari").Should().Be("English"); // lowercase
        _messageManager.NationalityCheck("QATARI").Should().Be("English"); // uppercase
        _messageManager.NationalityCheck("Qatari").Should().Be("Arabic");  // correct case
    }

    [Fact]
    public void GetPatientByHealthCardNumber_WithValidHealthCard_ShouldReturnPatient()
    {
        // Arrange
        var healthCardNumber = "HC12345678";
        var expectedPatient = TestHelper.CreateTestPatientEntity();
        
        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q => 
                q.EntityName == "contact" &&
                q.Criteria.Conditions.Any(c => 
                    c.AttributeName == "phcc_healthcardnumber" && 
                    c.Values.Contains(healthCardNumber)))))
            .Returns(expectedPatient);

        // Act
        var result = _messageManager.GetPatientByHealthCardNumber(healthCardNumber);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(expectedPatient.Id);
    }

    [Fact]
    public void GetPatientByHealthCardNumber_WithInvalidHealthCard_ShouldReturnNull()
    {
        // Arrange
        var healthCardNumber = "INVALID123";
        
        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.IsAny<QueryExpression>()))
            .Returns((Entity?)null);

        // Act
        var result = _messageManager.GetPatientByHealthCardNumber(healthCardNumber);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetLocationByCode_WithValidCode_ShouldReturnLocation()
    {
        // Arrange
        var locationCode = "LOC001";
        var expectedLocation = TestHelper.CreateTestLocationEntity();
        
        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q => 
                q.EntityName == "msemr_codeableconcept" &&
                q.Criteria.Conditions.Any(c => 
                    c.AttributeName == "msemr_code" && 
                    c.Values.Contains(locationCode)) &&
                q.Criteria.Conditions.Any(c => 
                    c.AttributeName == "phcc_iscancerscreeninghc" && 
                    c.Values.Contains(true)))))
            .Returns(expectedLocation);

        // Act
        var result = _messageManager.GetLocationByCode(locationCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(expectedLocation.Id);
    }

    [Fact]
    public void GetLocationByCode_WithInvalidCode_ShouldReturnNull()
    {
        // Arrange
        var locationCode = "INVALID";
        
        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.IsAny<QueryExpression>()))
            .Returns((Entity?)null);

        // Act
        var result = _messageManager.GetLocationByCode(locationCode);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void IsAppointmentReminder_WithFutureAppointment_ShouldReturnTrue()
    {
        // Arrange
        var futureAppointment = DateTime.UtcNow.AddDays(2);
        var arabTime = TimeZoneInfo.ConvertTimeFromUtc(futureAppointment, 
            TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"));
        const int reminderHours = 24;

        // Act
        var result = _messageManager.IsAppointmentReminder(arabTime, reminderHours);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsAppointmentReminder_WithPastAppointment_ShouldReturnFalse()
    {
        // Arrange
        var pastAppointment = DateTime.UtcNow.AddDays(-1);
        var arabTime = TimeZoneInfo.ConvertTimeFromUtc(pastAppointment, 
            TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"));
        const int reminderHours = 24;

        // Act
        var result = _messageManager.IsAppointmentReminder(arabTime, reminderHours);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsAppointmentReminder_WithAppointmentSoonButNotYetTime_ShouldReturnFalse()
    {
        // Arrange
        var soonAppointment = DateTime.UtcNow.AddHours(12); // 12 hours from now
        var arabTime = TimeZoneInfo.ConvertTimeFromUtc(soonAppointment, 
            TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"));
        const int reminderHours = 24; // Reminder should be sent 24 hours before

        // Act
        var result = _messageManager.IsAppointmentReminder(arabTime, reminderHours);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData(1)]
    [InlineData(6)]
    [InlineData(12)]
    [InlineData(24)]
    [InlineData(48)]
    public void IsAppointmentReminder_WithVariousReminderHours_ShouldCalculateCorrectly(int reminderHours)
    {
        // Arrange
        var appointmentTime = DateTime.UtcNow.AddHours(reminderHours + 1); // 1 hour after reminder time
        var arabTime = TimeZoneInfo.ConvertTimeFromUtc(appointmentTime, 
            TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"));

        // Act
        var result = _messageManager.IsAppointmentReminder(arabTime, reminderHours);

        // Assert
        result.Should().BeTrue();
    }
}
