using Core.Constants;

namespace TestPhccNotifier.Core.Constants;

public class MessageStatusTests
{
    [Theory]
    [InlineData(MessageStatus.Draft, 1)]
    [InlineData(MessageStatus.Sent, 2)]
    [InlineData(MessageStatus.Cancelled, 3)]
    [InlineData(MessageStatus.WaitingSend, 4)]
    [InlineData(MessageStatus.Retry, 100000005)]
    [InlineData(MessageStatus.Error, 100000004)]
    public void MessageStatus_ShouldHaveCorrectValues(MessageStatus messageStatus, int expectedValue)
    {
        // Act & Assert
        ((int)messageStatus).Should().Be(expectedValue);
    }

    [Fact]
    public void MessageStatus_ShouldHaveAllExpectedValues()
    {
        // Arrange
        var expectedValues = new[]
        {
            MessageStatus.Draft,
            MessageStatus.Sent,
            MessageStatus.Cancelled,
            MessageStatus.WaitingSend,
            MessageStatus.Retry,
            MessageStatus.Error
        };

        // Act
        var actualValues = Enum.GetValues<MessageStatus>();

        // Assert
        actualValues.Should().BeEquivalentTo(expectedValues);
    }

    [Fact]
    public void MessageStatus_ShouldBeConvertibleToInt()
    {
        // Arrange
        var messageStatus = MessageStatus.Draft;

        // Act
        int intValue = (int)messageStatus;

        // Assert
        intValue.Should().Be(1);
    }

    [Fact]
    public void MessageStatus_ShouldBeConvertibleFromInt()
    {
        // Arrange
        const int intValue = 1;

        // Act
        var messageStatus = (MessageStatus)intValue;

        // Assert
        messageStatus.Should().Be(MessageStatus.Draft);
    }

    [Theory]
    [InlineData(1, MessageStatus.Draft)]
    [InlineData(2, MessageStatus.Sent)]
    [InlineData(3, MessageStatus.Cancelled)]
    [InlineData(4, MessageStatus.WaitingSend)]
    [InlineData(100000005, MessageStatus.Retry)]
    [InlineData(100000004, MessageStatus.Error)]
    public void MessageStatus_IntToEnum_ShouldConvertCorrectly(int intValue, MessageStatus expectedMessageStatus)
    {
        // Act
        var messageStatus = (MessageStatus)intValue;

        // Assert
        messageStatus.Should().Be(expectedMessageStatus);
    }

    [Fact]
    public void MessageStatus_ToString_ShouldReturnEnumName()
    {
        // Arrange
        var messageStatus = MessageStatus.Draft;

        // Act
        var stringValue = messageStatus.ToString();

        // Assert
        stringValue.Should().Be("Draft");
    }

    [Fact]
    public void MessageStatus_ShouldBeDefined()
    {
        // Act & Assert
        foreach (MessageStatus messageStatus in Enum.GetValues<MessageStatus>())
        {
            Enum.IsDefined(typeof(MessageStatus), messageStatus).Should().BeTrue();
        }
    }

    [Theory]
    [InlineData(0)]
    [InlineData(5)]
    [InlineData(-1)]
    [InlineData(100)]
    [InlineData(100000000)]
    public void MessageStatus_InvalidValues_ShouldNotBeDefined(int invalidValue)
    {
        // Act & Assert
        Enum.IsDefined(typeof(MessageStatus), invalidValue).Should().BeFalse();
    }

    [Fact]
    public void MessageStatus_HighValueEnums_ShouldHaveCorrectValues()
    {
        // Assert - Test the high value enums specifically
        ((int)MessageStatus.Retry).Should().Be(100000005);
        ((int)MessageStatus.Error).Should().Be(100000004);
    }
}
