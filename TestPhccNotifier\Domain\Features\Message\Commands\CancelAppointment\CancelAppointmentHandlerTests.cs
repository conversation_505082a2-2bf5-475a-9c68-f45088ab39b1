using AutoFixture;
using Core.Exceptions;
using Domain.Features.Message;
using Domain.Features.Message.Commands.CancelAppointment;
using FluentValidation;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Domain.Features.Message.Commands.CancelAppointment;

public class CancelAppointmentHandlerTests
{
    private readonly Mock<ServiceClient> _mockServiceClient;
    private readonly MessageManager _messageManager;
    private readonly CancelAppointmentHandler _handler;
    private readonly IFixture _fixture;

    public CancelAppointmentHandlerTests()
    {
        _mockServiceClient = TestHelper.CreateMockServiceClient();
        _messageManager = new MessageManager(_mockServiceClient.Object);
        _handler = new CancelAppointmentHandler(_mockServiceClient.Object, _messageManager);
        _fixture = TestHelper.CreateFixture();
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldCreateMessageAndReturnResponse()
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();
        var messageId = Guid.NewGuid();
        var messageEntity = TestHelper.CreateTestMessageEntity();
        messageEntity["phcc_messageid"] = "MSG-CANCEL-12345";

        SetupMocksForSuccessfulCreation(request, messageId, messageEntity);

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        response.Id.Should().Be(messageId);
        response.MessageId.Should().Be("MSG-CANCEL-12345");
    }

    [Fact]
    public async Task Handle_WithPatientNotFound_ShouldThrowBusinessException()
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();

        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q =>
                q.EntityName == "contact")))
            .Returns((Entity?)null);

        // Act & Assert
        var exception = await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<BusinessException>();
        
        exception.Which.Message.Should().Contain("There is no patient");
    }

    [Fact]
    public async Task Handle_WithLocationNotFound_ShouldThrowBusinessException()
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();

        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q =>
                q.EntityName == "contact")))
            .Returns(TestHelper.CreateTestPatientEntity());

        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q =>
                q.EntityName == "msemr_codeableconcept")))
            .Returns((Entity?)null);

        // Act & Assert
        var exception = await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<BusinessException>();
        
        exception.Which.Message.Should().Contain("There is no location");
    }

    [Fact]
    public async Task Handle_ShouldUpdateExistingMessages()
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();
        var messageId = Guid.NewGuid();
        var messageEntity = TestHelper.CreateTestMessageEntity();
        var existingMessages = new List<Entity> { TestHelper.CreateTestMessageEntity() };

        SetupMocksForSuccessfulCreation(request, messageId, messageEntity);

        _mockServiceClient
            .Setup(x => x.RetrieveMultipleAsync(It.IsAny<QueryExpression>()))
            .ReturnsAsync(new EntityCollection(existingMessages));

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        
        // Verify that existing messages were updated
        _mockServiceClient.Verify(x => x.UpdateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()), 
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithInvalidRequest_ShouldThrowValidationException()
    {
        // Arrange
        var invalidRequest = new CancelAppointmentRequest
        {
            CustomIdentifier = "INVALID", // Should start with HC and be 10 chars
            AppointmentDateTime = default // Should not be empty
        };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(invalidRequest, CancellationToken.None))
            .Should().ThrowAsync<ValidationException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task Handle_WithEmptyAppointmentIdentifier_ShouldThrowValidationException(string appointmentIdentifier)
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();
        request.AppointmentIdentifier = appointmentIdentifier;

        // Act & Assert
        await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<ValidationException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("HC123")] // Too short
    [InlineData("HC12345678901")] // Too long
    [InlineData("AB1234567890")] // Doesn't start with HC
    public async Task Handle_WithInvalidCustomIdentifier_ShouldThrowValidationException(string customIdentifier)
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();
        request.CustomIdentifier = customIdentifier;

        // Act & Assert
        await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task Handle_ShouldCreateEntityWithCorrectMessageType()
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();
        var messageId = Guid.NewGuid();
        var messageEntity = TestHelper.CreateTestMessageEntity();

        SetupMocksForSuccessfulCreation(request, messageId, messageEntity);

        Entity? capturedEntity = null;
        _mockServiceClient
            .Setup(x => x.CreateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()))
            .Callback<Entity, CancellationToken>((entity, token) => capturedEntity = entity)
            .ReturnsAsync(messageId);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        capturedEntity.Should().NotBeNull();
        capturedEntity!.LogicalName.Should().Be("phcc_message");
        capturedEntity.GetAttributeValue<OptionSetValue>("phcc_messagetype").Value.Should().Be(2); // CancelAppointment
    }

    private CancelAppointmentRequest CreateValidCancelAppointmentRequest()
    {
        return new CancelAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari",
            ProcedureName = "Mammography",
            ProcedureCode = "MAM001"
        };
    }

    private void SetupMocksForSuccessfulCreation(CancelAppointmentRequest request, Guid messageId, Entity messageEntity)
    {
        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q =>
                q.EntityName == "contact")))
            .Returns(TestHelper.CreateTestPatientEntity());

        _mockServiceClient
            .Setup(x => x.RetrieveFirst(It.Is<QueryExpression>(q =>
                q.EntityName == "msemr_codeableconcept")))
            .Returns(TestHelper.CreateTestLocationEntity());

        _mockServiceClient
            .Setup(x => x.CreateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(messageId);

        _mockServiceClient
            .Setup(x => x.Retrieve("phcc_message", messageId, It.IsAny<ColumnSet>()))
            .Returns(messageEntity);

        _mockServiceClient
            .Setup(x => x.RetrieveMultipleAsync(It.IsAny<QueryExpression>()))
            .ReturnsAsync(new EntityCollection());

        _mockServiceClient
            .Setup(x => x.UpdateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
    }
}
