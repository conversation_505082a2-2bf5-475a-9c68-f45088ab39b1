<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <LangVersion>latest</LangVersion>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Moq" Version="4.20.69"/>
        <PackageReference Include="FluentAssertions" Version="6.12.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.25"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.4"/>
        <PackageReference Include="AutoFixture" Version="4.18.0"/>
        <PackageReference Include="AutoFixture.Xunit2" Version="4.18.0"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
        <Using Include="FluentAssertions"/>
        <Using Include="Moq"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\WebAPI\WebAPI.csproj"/>
        <ProjectReference Include="..\Domain\Domain.csproj"/>
        <ProjectReference Include="..\Core\Core.csproj"/>
    </ItemGroup>

</Project>
