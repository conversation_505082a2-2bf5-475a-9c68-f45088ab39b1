using Core.Exceptions;

namespace TestPhccNotifier.Core.Exceptions;

public class BusinessExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_ShouldSetMessage()
    {
        // Arrange
        const string expectedMessage = "Test business error message";

        // Act
        var exception = new BusinessException(expectedMessage);

        // Assert
        exception.Message.Should().Be(expectedMessage);
        exception.Should().BeOfType<BusinessException>();
        exception.Should().BeAssignableTo<Exception>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("Simple message")]
    [InlineData("Complex message with special characters: !@#$%^&*()")]
    [InlineData("Very long message that contains multiple words and should be handled properly by the exception class")]
    public void Constructor_WithVariousMessages_ShouldSetMessageCorrectly(string message)
    {
        // Act
        var exception = new BusinessException(message);

        // Assert
        exception.Message.Should().Be(message);
    }

    [Fact]
    public void Constructor_WithNullMessage_ShouldHandleGracefully()
    {
        // Act & Assert
        var exception = new BusinessException(null!);
        exception.Should().NotBeNull();
        exception.Message.Should().BeNull();
    }

    [Fact]
    public void BusinessException_ShouldInheritFromException()
    {
        // Arrange
        var exception = new BusinessException("Test");

        // Assert
        exception.Should().BeAssignableTo<Exception>();
    }

    [Fact]
    public void BusinessException_ShouldBeSerializable()
    {
        // Arrange
        const string message = "Test serialization";
        var originalException = new BusinessException(message);

        // Act & Assert
        originalException.Should().BeBinarySerializable();
    }
}
