using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Core.Dynamics;

namespace TestPhccNotifier.TestUtilities;

public class ServiceClientWrapper : IServiceClientWrapper
{
    private readonly ServiceClient _serviceClient;

    public ServiceClientWrapper(ServiceClient serviceClient)
    {
        _serviceClient = serviceClient;
    }

    public bool IsReady => _serviceClient.IsReady;

    public async Task<EntityCollection> RetrieveMultipleAsync(QueryExpression query)
    {
        return await _serviceClient.RetrieveMultipleAsync(query);
    }

    public Entity? RetrieveFirst(QueryExpression query)
    {
        return _serviceClient.RetrieveFirst(query);
    }

    public async Task<Guid> CreateAsync(Entity entity, CancellationToken cancellationToken)
    {
        return await _serviceClient.CreateAsync(entity, cancellationToken);
    }

    public async Task<Entity> RetrieveAsync(string entityName, Guid id, ColumnSet columnSet, CancellationToken cancellationToken)
    {
        return await _serviceClient.RetrieveAsync(entityName, id, columnSet, cancellationToken);
    }
}
